# Video2Frame Usage Examples

This document provides usage examples for the `video2frame.py` script that converts MP4 video files to individual frame images.

## Prerequisites

Make sure you have the required dependencies installed:
```bash
pip install opencv-python tqdm
```

Or use an existing conda environment like `sam2_env_py310`:
```bash
conda activate sam2_env_py310
```

## Basic Usage Examples

### 1. Process a Single Video File

Extract frames from a single video file:
```bash
python video2frame.py --input sam2/notebooks/videos/bedroom.mp4
```

This will create a folder `sam2/notebooks/videos/bedroom_frames/` containing:
- `frame_00001.jpg`
- `frame_00002.jpg`
- `frame_00003.jpg`
- ... (all frames from the video)

### 2. Process with Custom Output Directory

Extract frames to a specific output directory:
```bash
python video2frame.py --input sam2/notebooks/videos/bedroom.mp4 --output_dir ./extracted_frames
```

This will create: `./extracted_frames/bedroom_frames/`

### 3. Process with Different Image Format

Extract frames as PNG images:
```bash
python video2frame.py --input sam2/notebooks/videos/bedroom.mp4 --format png
```

### 4. Batch Process All Videos in Directory

Process all video files in a directory:
```bash
python video2frame.py --input_dir sam2/notebooks/videos --batch
```

This will process all MP4, AVI, MOV, MKV, and WMV files in the directory.

### 5. Batch Process with Custom Output

Batch process with custom output directory:
```bash
python video2frame.py --input_dir sam2/notebooks/videos --batch --output_dir ./all_frames
```

## Expected Output Structure

For a video file `notebooks/videos/01.mp4`, the script will create:

```
notebooks/videos/01_frames/
├── frame_00001.jpg
├── frame_00002.jpg
├── frame_00003.jpg
├── ...
└── frame_XXXXX.jpg
```

## Supported Video Formats

- MP4 (`.mp4`)
- AVI (`.avi`)
- MOV (`.mov`)
- MKV (`.mkv`)
- WMV (`.wmv`)

## Supported Output Image Formats

- JPG/JPEG (default)
- PNG
- BMP

## Error Handling

The script includes comprehensive error handling for:
- Missing input files
- Unsupported video formats
- File permission issues
- Video reading errors
- Frame extraction failures

## Performance Notes

- Frame extraction speed depends on video resolution and system performance
- Progress is displayed using a progress bar
- Typical performance: ~400 frames/second for 960x540 resolution videos

## Integration with SAM2 Workflow

This script is designed to work seamlessly with the SAM2 video processing pipeline:

1. **Extract frames** using `video2frame.py`
2. **Process frames** with SAM2 video predictor
3. **Generate annotations** and tracking results

The output format is compatible with SAM2's expected input structure for video processing.
