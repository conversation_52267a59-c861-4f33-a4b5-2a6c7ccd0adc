import os
import cv2
import argparse
import sys
from pathlib import Path
from tqdm import tqdm

"""
Video to Frame Extraction Tool

This script converts MP4 video files to individual frame images.
It follows the coding style and structure conventions from the SAM2 project.

Features:
- Extract frames from single video files or batch process directories
- Support for multiple video formats (MP4, AVI, MOV, MKV, WMV)
- Configurable output formats (JPG, PNG, BMP)
- Progress tracking with tqdm
- Automatic output folder creation with naming convention: {video_name}_frames
- Error handling for file operations and video processing

Usage:
    # Process single video file
    python video2frame.py --input notebooks/videos/01.mp4

    # Batch process all videos in directory
    python video2frame.py --input_dir notebooks/videos --batch

    # Custom output directory and format
    python video2frame.py --input video.mp4 --output_dir ./frames --format png

    # Help
    python video2frame.py --help

Output Structure:
    For input: notebooks/videos/01.mp4
    Output: notebooks/videos/01_frames/
        ├── frame_00001.jpg
        ├── frame_00002.jpg
        └── ...
"""

def setup_output_folder(video_path, output_base_dir=None):
    """
    Setup output folder structure for extracted frames.
    
    Args:
        video_path (str): Path to the input video file
        output_base_dir (str, optional): Base directory for output. If None, uses video's parent directory
    
    Returns:
        Path: Output directory path for frames
    """
    video_path = Path(video_path)
    video_name = video_path.stem  # filename without extension
    
    if output_base_dir is None:
        output_base_dir = video_path.parent
    else:
        output_base_dir = Path(output_base_dir)
    
    # Create output folder: video_name + "_frames"
    output_folder = output_base_dir / f"{video_name}_frames"
    output_folder.mkdir(parents=True, exist_ok=True)
    
    return output_folder

def extract_frames_from_video(video_path, output_folder, frame_format="jpg", frame_pattern="{:05d}"):
    """
    Extract all frames from a video file and save them as individual images.
    
    Args:
        video_path (str): Path to the input video file
        output_folder (Path): Directory to save extracted frames
        frame_format (str): Image format for saved frames (jpg, png, etc.)
        frame_pattern (str): Naming pattern for frame files
    
    Returns:
        tuple: (success: bool, frame_count: int, error_message: str)
    """
    try:
        # Open video file
        cap = cv2.VideoCapture(str(video_path))
        
        if not cap.isOpened():
            return False, 0, f"Error: Could not open video file {video_path}"
        
        # Get video properties
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        print(f"Video Info:")
        print(f"  - Total frames: {total_frames}")
        print(f"  - FPS: {fps:.2f}")
        print(f"  - Resolution: {width}x{height}")
        print(f"  - Output folder: {output_folder}")
        
        frame_count = 0
        
        # Extract frames with progress bar
        with tqdm(total=total_frames, desc="Extracting frames") as pbar:
            while True:
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                # Generate frame filename
                frame_filename = f"frame_{frame_pattern.format(frame_count + 1)}.{frame_format}"
                frame_path = output_folder / frame_filename
                
                # Save frame
                success = cv2.imwrite(str(frame_path), frame)
                
                if not success:
                    cap.release()
                    return False, frame_count, f"Error: Could not save frame {frame_filename}"
                
                frame_count += 1
                pbar.update(1)
        
        cap.release()
        
        print(f"Successfully extracted {frame_count} frames to {output_folder}")
        return True, frame_count, ""
        
    except Exception as e:
        return False, 0, f"Error during frame extraction: {str(e)}"

def process_single_video(video_path, output_base_dir=None, frame_format="jpg"):
    """
    Process a single video file and extract frames.
    
    Args:
        video_path (str): Path to the video file
        output_base_dir (str, optional): Base directory for output
        frame_format (str): Image format for frames
    
    Returns:
        bool: Success status
    """
    video_path = Path(video_path)
    
    # Validate input file
    if not video_path.exists():
        print(f"Error: Video file not found: {video_path}")
        return False
    
    if not video_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.mkv', '.wmv']:
        print(f"Error: Unsupported video format: {video_path.suffix}")
        return False
    
    print(f"\nProcessing video: {video_path}")
    
    # Setup output folder
    output_folder = setup_output_folder(video_path, output_base_dir)
    
    # Extract frames
    success, frame_count, error_msg = extract_frames_from_video(
        video_path, output_folder, frame_format
    )
    
    if not success:
        print(f"Failed to process {video_path}: {error_msg}")
        return False
    
    return True

def batch_process_videos(input_dir, output_base_dir=None, frame_format="jpg"):
    """
    Batch process all MP4 files in a directory.
    
    Args:
        input_dir (str): Directory containing video files
        output_base_dir (str, optional): Base directory for output
        frame_format (str): Image format for frames
    
    Returns:
        tuple: (success_count: int, total_count: int)
    """
    input_dir = Path(input_dir)
    
    if not input_dir.exists():
        print(f"Error: Input directory not found: {input_dir}")
        return 0, 0
    
    # Find all video files (case-insensitive)
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv']
    video_files = []

    # Get all files and filter by extension (case-insensitive)
    all_files = [f for f in input_dir.iterdir() if f.is_file()]
    for file_path in all_files:
        if file_path.suffix.lower() in video_extensions:
            video_files.append(file_path)

    # Remove duplicates and sort
    video_files = sorted(list(set(video_files)))

    if not video_files:
        print(f"No video files found in {input_dir}")
        return 0, 0
    
    print(f"Found {len(video_files)} video file(s) to process:")
    for video_file in video_files:
        print(f"  - {video_file.name}")
    
    success_count = 0
    
    for video_file in video_files:
        if process_single_video(video_file, output_base_dir, frame_format):
            success_count += 1
        print("-" * 50)
    
    print(f"\nBatch processing completed: {success_count}/{len(video_files)} videos processed successfully")
    return success_count, len(video_files)

def main():
    """Main function to handle command line arguments and execute frame extraction."""
    parser = argparse.ArgumentParser(
        description="Convert MP4 video files to individual frame images",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python video2frame.py --input notebooks/videos/01.mp4
  python video2frame.py --input_dir notebooks/videos --batch
  python video2frame.py --input bedroom.mp4 --output_dir ./frames --format png
        """
    )
    
    # Input options (mutually exclusive)
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        "--input", "-i",
        type=str,
        help="Path to a single video file to process"
    )
    input_group.add_argument(
        "--input_dir", "-d",
        type=str,
        help="Directory containing video files for batch processing"
    )
    
    # Optional arguments
    parser.add_argument(
        "--batch", "-b",
        action="store_true",
        help="Enable batch processing mode (used with --input_dir)"
    )
    parser.add_argument(
        "--output_dir", "-o",
        type=str,
        default=None,
        help="Base output directory (default: same as input video directory)"
    )
    parser.add_argument(
        "--format", "-f",
        type=str,
        default="jpg",
        choices=["jpg", "jpeg", "png", "bmp"],
        help="Output image format (default: jpg)"
    )
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.input_dir and not args.batch:
        print("Error: --batch flag is required when using --input_dir")
        sys.exit(1)
    
    try:
        if args.input:
            # Process single video
            success = process_single_video(args.input, args.output_dir, args.format)
            sys.exit(0 if success else 1)
        
        elif args.input_dir and args.batch:
            # Batch process videos
            success_count, total_count = batch_process_videos(
                args.input_dir, args.output_dir, args.format
            )
            sys.exit(0 if success_count == total_count else 1)
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
